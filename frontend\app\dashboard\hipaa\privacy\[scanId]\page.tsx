'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { ArrowLeft, Shield, FileText, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { HipaaPrivacyResultsPage } from '@/components/hipaa-privacy';
import { HipaaPrivacyScanResult } from '@/types/hipaa-privacy';
import { ComplianceBreadcrumb, createComplianceBreadcrumbs } from '@/components/navigation';

/**
 * HIPAA Privacy Module Scan Results Page
 * Enhanced version of the privacy results with improved navigation and integration
 */
export default function HipaaPrivacyScanResultPage() {
  const params = useParams();
  const router = useRouter();
  const scanId = params.scanId as string;

  const [scanResult, setScanResult] = useState<HipaaPrivacyScanResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!scanId) {
      setError('Invalid scan ID');
      setLoading(false);
      return;
    }

    // Fetch scan result from API
    fetchScanResult(scanId);
  }, [scanId]);

  const fetchScanResult = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      // First, try to fetch from API (database-stored results)
      const apiUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3001/api/v1';

      try {
        const response = await fetch(`${apiUrl}/hipaa-privacy/scan/${id}/result`);

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data) {
            setScanResult(data.data);
            return;
          }
        }
      } catch (apiError) {
        console.warn('Failed to fetch from API, trying localStorage:', apiError);
      }

      // Fallback: Try to get the result from localStorage (for immediate results)
      const localStorageKey = `privacy-scan-${id}`;
      const cachedResult = localStorage.getItem(localStorageKey);

      if (cachedResult) {
        try {
          const parsedResult = JSON.parse(cachedResult);
          setScanResult(parsedResult);
          // Clean up localStorage after successful load
          localStorage.removeItem(localStorageKey);
          return;
        } catch (parseError) {
          console.warn('Failed to parse cached scan result:', parseError);
          localStorage.removeItem(localStorageKey);
        }
      }

      // If both methods fail, show error
      throw new Error('Scan result not found in database or local storage');
    } catch (err) {
      console.error('Error fetching scan result:', err);
      setError(err instanceof Error ? err.message : 'Failed to load scan result');
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = () => {
    // TODO: Implement export functionality
    console.log('Exporting report for scan:', scanId);
  };

  const handleStartNewScan = () => {
    router.push('/dashboard/hipaa/privacy-scan');
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard/hipaa');
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        {/* Header with loading state */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBackToDashboard}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to HIPAA Dashboard
          </Button>
          <div className="flex items-center gap-3">
            <Shield className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">HIPAA Privacy Scan Results</h1>
              <p className="text-gray-600">Loading scan details...</p>
            </div>
          </div>
        </div>

        {/* Loading skeleton */}
        <div className="space-y-6">
          <Card className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBackToDashboard}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to HIPAA Dashboard
          </Button>
          <div className="flex items-center gap-3">
            <Shield className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">HIPAA Privacy Scan Results</h1>
              <p className="text-gray-600">Error loading scan results</p>
            </div>
          </div>
        </div>

        {/* Error display */}
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="text-center">
              <FileText className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Failed to Load Scan Results
              </h3>
              <p className="text-red-600 mb-4">{error}</p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => fetchScanResult(scanId)} variant="outline">
                  Try Again
                </Button>
                <Button onClick={handleBackToDashboard}>Back to Dashboard</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!scanResult) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBackToDashboard}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to HIPAA Dashboard
          </Button>
          <div className="flex items-center gap-3">
            <Shield className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">HIPAA Privacy Scan Results</h1>
              <p className="text-gray-600">Scan not found</p>
            </div>
          </div>
        </div>

        {/* Not found display */}
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Scan Not Found</h3>
              <p className="text-gray-600 mb-4">
                The requested scan (ID: {scanId}) could not be found.
              </p>
              <Button onClick={handleBackToDashboard}>Back to Dashboard</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" style={{ backgroundColor: '#F5F5F5', minHeight: '100vh' }}>
      {/* Enhanced Header with Navigation */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleBackToDashboard}
          style={{ borderColor: '#0055A4', color: '#0055A4' }}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to HIPAA Dashboard
        </Button>
        <div className="flex items-center gap-3 flex-1">
          <Shield className="h-8 w-8" style={{ color: '#0055A4' }} />
          <div>
            <h1 className="text-3xl font-bold" style={{ color: '#333333' }}>HIPAA Privacy Scan Results</h1>
            <div className="flex items-center gap-2 mt-1">
              <p style={{ color: '#666666' }}>
                Scan ID: <span className="font-mono text-sm">{scanId}</span>
              </p>
              <Badge variant="outline">Privacy Policy Analysis</Badge>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            asChild
            style={{ borderColor: '#0055A4', color: '#0055A4' }}
          >
            <Link href="/dashboard/hipaa">
              <FileText className="h-4 w-4 mr-2" />
              Dashboard
            </Link>
          </Button>
          <Button
            variant="outline"
            size="sm"
            asChild
            style={{ borderColor: '#0055A4', color: '#0055A4' }}
          >
            <Link href={scanResult.targetUrl} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              View Site
            </Link>
          </Button>
        </div>
      </div>

      {/* Breadcrumb Navigation */}
      <ComplianceBreadcrumb items={createComplianceBreadcrumbs.hipaaPrivacyResult(scanId)} />

      {/* Main Results Component */}
      <HipaaPrivacyResultsPage
        scanResult={scanResult}
        onExportReport={handleExportReport}
        onStartNewScan={handleStartNewScan}
      />

      {/* Additional Actions */}
      <Card style={{ backgroundColor: 'white', border: '1px solid #E5E7EB' }}>
        <CardHeader>
          <CardTitle style={{ color: '#333333' }}>Additional Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              variant="outline"
              onClick={handleStartNewScan}
              style={{
                backgroundColor: 'white',
                color: '#333333',
                borderColor: '#0055A4'
              }}
            >
              <FileText className="h-4 w-4 mr-2" />
              Start New Privacy Scan
            </Button>
            <Button
              variant="outline"
              asChild
              style={{
                backgroundColor: 'white',
                color: '#333333',
                borderColor: '#0055A4'
              }}
            >
              <Link href="/dashboard/hipaa/security-scan">
                <Shield className="h-4 w-4 mr-2" />
                Run Security Scan
              </Link>
            </Button>
            <Button
              variant="outline"
              asChild
              style={{
                backgroundColor: 'white',
                color: '#333333',
                borderColor: '#0055A4'
              }}
            >
              <Link href="/dashboard/hipaa/security">View All Scans</Link>
            </Button>
            <Button
              variant="outline"
              asChild
              style={{
                backgroundColor: 'white',
                color: '#333333',
                borderColor: '#0055A4'
              }}
            >
              <Link href="/dashboard/guidance">Compliance Guidance</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

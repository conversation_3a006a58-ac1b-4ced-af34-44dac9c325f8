import { Router, Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { HipaaPrivacyPolicyOrchestrator } from '../compliance/hipaa/privacy/services/privacy-orchestrator';
import { InputValidator, AuditLogger } from '../config/security';
import { HipaaScanOptions } from '../compliance/hipaa/privacy/types';
import { HipaaDatabase } from '../compliance/hipaa/privacy/database/hipaa-privacy-db';
import knex from '../lib/db';

// Import express-validator functions directly
import { body, param, query, validationResult } from 'express-validator';

const router = Router();
const orchestrator = new HipaaPrivacyPolicyOrchestrator();

// Helper function to calculate risk level based on issues
const calculateRiskLevel = (
  criticalIssues: number,
  highIssues: number,
  mediumIssues: number,
  _lowIssues: number,
): 'critical' | 'high' | 'medium' | 'low' => {
  if (criticalIssues > 0) return 'critical';
  if (highIssues > 0) return 'high';
  if (mediumIssues > 0) return 'medium';
  return 'low';
};

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Start HIPAA Privacy Scan
router.post(
  '/scan',
  [
    body('targetUrl')
      .isURL({ require_protocol: true, protocols: ['http', 'https'] })
      .withMessage('Valid URL is required'),
    body('timeout')
      .optional()
      .isInt({ min: 30000, max: 600000 })
      .withMessage('timeout must be between 30 seconds and 10 minutes'),
    body('maxRedirects')
      .optional()
      .isInt({ min: 0, max: 10 })
      .withMessage('maxRedirects must be between 0 and 10'),
    body('enableLevel1').optional().isBoolean().withMessage('enableLevel1 must be a boolean'),
    body('enableLevel2').optional().isBoolean().withMessage('enableLevel2 must be a boolean'),
    body('enableLevel3').optional().isBoolean().withMessage('enableLevel3 must be a boolean'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const {
        targetUrl,
        timeout = 300000,
        maxRedirects = 5,
        userAgent = 'HIPAA-Privacy-Scanner/1.0',
        includeSubdomains = false,
        enableLevel1 = true,
        enableLevel2 = true,
        enableLevel3 = false,
        cacheResults = true,
        generateReport = true,
      } = req.body;

      // Validate URL security
      const isValidUrl = InputValidator.isValidUrl(targetUrl);
      if (!isValidUrl) {
        return res.status(400).json({
          success: false,
          error: 'Invalid URL',
          message: 'The provided URL is not valid or is blocked for security reasons',
        });
      }

      // Log privacy scan initiation
      console.log('🚀 HIPAA Privacy scan initiated via API');
      console.log('📋 Request details:', {
        targetUrl,
        timeout,
        maxRedirects,
        enableLevel1,
        enableLevel2,
        enableLevel3,
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
      });

      AuditLogger.logSecurityEvent(
        'hipaa_privacy_scan_started',
        {
          targetUrl,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
        },
        req,
      );

      const scanOptions: HipaaScanOptions = {
        timeout,
        maxRedirects,
        userAgent,
        includeSubdomains,
        enableLevel1,
        enableLevel2,
        enableLevel3,
        cacheResults,
        generateReport,
      };

      console.log('🔍 Starting HIPAA Privacy scan with options:', scanOptions);

      const result = await orchestrator.performComprehensiveScan(targetUrl, scanOptions);

      console.log('✅ HIPAA Privacy scan completed successfully');
      console.log('📊 Scan results summary:', {
        overallScore: result.overallScore,
        overallPassed: result.overallPassed,
        totalChecks: result.checks.length,
        recommendations: result.recommendations.length,
      });

      // Save scan results to database with proper UUID
      const scanId = uuidv4();
      try {
        // Get the first available user ID (for demo purposes)
        const [existingUser] = await knex('users').select('id').limit(1);
        const userId = (req as { user?: { id: string } }).user?.id || existingUser?.id || null;

        // First create a record in the scans table
        const [scanRecord] = await knex('scans')
          .insert({
            id: scanId,
            user_id: userId,
            url: targetUrl,
            status: 'completed',
            standards_scanned: JSON.stringify(['hipaa']), // Properly stringify the array
            summary_report: JSON.stringify({
              overallScore: result.overallScore,
              overallPassed: result.overallPassed,
              totalChecks: result.checks.length,
              recommendations: result.recommendations.length,
            }),
            completed_at: new Date(),
          })
          .returning('id');

        console.log('✅ Scan record created in scans table with ID:', scanRecord.id);

        // Then save the detailed HIPAA results
        await HipaaDatabase.saveScanResult(scanRecord.id, result);
        console.log('✅ Privacy scan results saved to database with ID:', scanRecord.id);
      } catch (dbError) {
        console.error('⚠️ Failed to save privacy scan results to database:', dbError);
        // Continue with response even if database save fails
      }

      AuditLogger.logSecurityEvent(
        'hipaa_privacy_scan_completed',
        {
          targetUrl,
          overallScore: result.overallScore,
          overallPassed: result.overallPassed,
          scanDuration: Date.now() - new Date(result.timestamp).getTime(),
        },
        req,
      );

      res.json({
        success: true,
        data: {
          scanId,
          status: 'completed',
          message: 'HIPAA privacy scan completed successfully',
          result,
        },
      });
    } catch (error) {
      console.error('❌ HIPAA Privacy scan failed:', error);

      AuditLogger.logSecurityEvent(
        'hipaa_privacy_scan_failed',
        {
          targetUrl: req.body.targetUrl,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        req,
      );

      res.status(500).json({
        success: false,
        error: 'HIPAA privacy scan failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get Scan Status (placeholder for future async implementation)
router.get(
  '/scan/:scanId/status',
  [param('scanId').notEmpty().withMessage('Scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;

      // For now, return completed status since scans are synchronous
      res.json({
        success: true,
        data: {
          scanId,
          status: 'completed',
          progress: 100,
          message: 'Scan completed',
        },
      });
    } catch (error) {
      console.error('Get scan status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scan status',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get Scan Result
router.get(
  '/scan/:scanId/result',
  [param('scanId').notEmpty().withMessage('Scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId } = req.params;

      // Try to get the result from the database
      const result = await HipaaDatabase.getScanResult(scanId);

      if (!result) {
        return res.status(404).json({
          success: false,
          error: 'Scan result not found',
          message: 'The requested privacy scan result was not found in the database.',
        });
      }

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Get scan result error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scan result',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Get All Scans
router.get(
  '/scans',
  [
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('limit must be between 1 and 100'),
    query('offset')
      .optional()
      .isInt({ min: 0 })
      .withMessage('offset must be a non-negative integer'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      console.log('📋 Fetching privacy scans from database...');
      console.log('🔍 Query parameters:', { limit, offset });

      // Get scans from the database using the HipaaDatabase
      const scans = await knex('hipaa_scans')
        .join('scans', 'hipaa_scans.scan_id', 'scans.id')
        .select(
          'hipaa_scans.id',
          'hipaa_scans.target_url as targetUrl',
          'hipaa_scans.overall_score as overallScore',
          'hipaa_scans.overall_passed as overallPassed',
          'hipaa_scans.compliance_level as complianceLevel',
          'hipaa_scans.total_checks as totalChecks',
          'hipaa_scans.passed_checks as passedChecks',
          'hipaa_scans.failed_checks as failedChecks',
          'hipaa_scans.critical_issues as criticalIssues',
          'hipaa_scans.high_issues as highIssues',
          'hipaa_scans.medium_issues as mediumIssues',
          'hipaa_scans.low_issues as lowIssues',
          'hipaa_scans.created_at as timestamp',
          'scans.status',
        )
        .orderBy('hipaa_scans.created_at', 'desc')
        .limit(limit)
        .offset(offset);

      console.log(`✅ Found ${scans.length} privacy scans in database`);

      // Transform the data to match the expected format
      const transformedScans = scans.map((scan) => ({
        scanId: scan.id,
        targetUrl: scan.targetUrl,
        timestamp: scan.timestamp,
        overallScore: scan.overallScore,
        overallPassed: scan.overallPassed,
        summary: {
          totalChecks: scan.totalChecks,
          passedChecks: scan.passedChecks,
          failedChecks: scan.failedChecks,
          criticalIssues: scan.criticalIssues,
          highIssues: scan.highIssues,
          mediumIssues: scan.mediumIssues,
          lowIssues: scan.lowIssues,
          overallScore: scan.overallScore,
          complianceLevel: scan.complianceLevel,
          riskLevel: calculateRiskLevel(
            scan.criticalIssues,
            scan.highIssues,
            scan.mediumIssues,
            scan.lowIssues,
          ),
        },
        status: scan.status || 'completed',
      }));

      res.json({
        success: true,
        data: transformedScans,
        metadata: {
          count: transformedScans.length,
          limit,
          offset,
          hasMore: transformedScans.length === limit,
        },
      });
    } catch (error) {
      console.error('❌ Get all privacy scans error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get scans',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Delete Scan (placeholder for future database storage)
router.delete(
  '/scan/:scanId',
  [param('scanId').notEmpty().withMessage('Scan ID is required')],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId: _scanId } = req.params;

      res.status(404).json({
        success: false,
        error: 'Scan not found',
        message: 'Privacy scans are not stored persistently yet.',
      });
    } catch (error) {
      console.error('Delete scan error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete scan',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Export Scan Report
router.get(
  '/scan/:scanId/export',
  [
    param('scanId').notEmpty().withMessage('Scan ID is required'),
    query('format').optional().isIn(['pdf', 'json']).withMessage('format must be pdf or json'),
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const { scanId: _scanId } = req.params;
      const _format = (req.query.format as string) || 'pdf';

      res.status(501).json({
        success: false,
        error: 'Export not yet implemented',
        message: 'Privacy scan export functionality will be available in a future update.',
      });
    } catch (error) {
      console.error('Export scan error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export scan',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },
);

// Health check endpoint
router.get('/health', async (req: Request, res: Response) => {
  try {
    res.json({
      success: true,
      data: {
        service: 'HIPAA Privacy Scanner',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      },
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      success: false,
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;

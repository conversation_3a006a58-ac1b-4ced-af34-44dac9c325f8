'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import {
  ArrowLeft,
  Lock,
  Shield,
  Plus,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  FileText,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ComplianceBreadcrumb, createComplianceBreadcrumbs } from '@/components/navigation';
import { hipaaDashboardService } from '@/services/hipaa-dashboard-api';

interface SecurityScanSummary {
  id: string;
  targetUrl: string;
  timestamp: string;
  overallScore: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
  status: 'completed' | 'failed' | 'running';
  passedTests: number;
  failedTests: number;
  vulnerabilities: number;
}

/**
 * HIPAA Security Module Results Listing Page
 * Shows all security scan results with filtering and sorting options
 */
export default function HipaaSecurityResultsPage() {
  const router = useRouter();
  const [scans, setScans] = useState<SecurityScanSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSecurityScans();
  }, []);

  const fetchSecurityScans = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the dashboard service to get security scans
      const securityScans = await hipaaDashboardService.getSecurityScans();

      // Convert to the format expected by this component
      const scanSummaries: SecurityScanSummary[] = securityScans.map((scan) => ({
        id: scan.scanId || `security-${Date.now()}-${Math.random()}`,
        targetUrl: scan.targetUrl || 'Unknown URL',
        timestamp: typeof scan.scanTimestamp === 'string' ? scan.scanTimestamp : scan.scanTimestamp?.toISOString() || new Date().toISOString(),
        overallScore: scan.overallScore || 0,
        riskLevel: scan.riskLevel || 'medium',
        status: 'completed', // Assuming completed for now
        passedTests: scan.passedTests?.length || 0,
        failedTests: scan.failedTests?.length || 0,
        vulnerabilities: scan.vulnerabilities?.length || 0,
      }));

      setScans(scanSummaries);
    } catch (err) {
      console.error('Error fetching security scans:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return 'destructive';
      case 'high':
        return 'warning';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'secondary';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'destructive';
      case 'running':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard/hipaa');
  };

  if (loading) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: '#F5F5F5', color: '#333333' }}>
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToDashboard}
              style={{
                borderColor: '#0055A4',
                color: '#0055A4',
                backgroundColor: 'white',
              }}
              className="hover:bg-blue-50 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to HIPAA Dashboard
            </Button>
            <div className="flex items-center gap-3">
              <Lock className="h-8 w-8" style={{ color: '#663399' }} />
              <div>
                <h1 className="text-3xl font-bold" style={{ color: '#333333' }}>
                  Security Scans
                </h1>
                <p style={{ color: '#666666' }}>Loading scan results...</p>
              </div>
            </div>
          </div>

          {/* Loading skeleton */}
          <div className="grid gap-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#F5F5F5', color: '#333333' }}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToDashboard}
              style={{
                borderColor: '#0055A4',
                color: '#0055A4',
                backgroundColor: 'white',
              }}
              className="hover:bg-blue-50 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to HIPAA Dashboard
            </Button>
            <div className="flex items-center gap-3">
              <Lock className="h-8 w-8" style={{ color: '#663399' }} />
              <div>
                <h1 className="text-3xl font-bold" style={{ color: '#333333' }}>
                  Security Scans
                </h1>
                <p style={{ color: '#666666' }}>
                  {scans.length} scan{scans.length !== 1 ? 's' : ''} completed
                </p>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              asChild
              style={{
                backgroundColor: '#0055A4',
                color: 'white',
                border: 'none',
              }}
              className="hover:bg-blue-700 transition-colors shadow-md"
            >
              <Link href="/dashboard/hipaa/security-scan">
                <Plus className="h-4 w-4 mr-2" />
                New Security Scan
              </Link>
            </Button>
          </div>
        </div>

        {/* Breadcrumb Navigation */}
        <ComplianceBreadcrumb items={createComplianceBreadcrumbs.hipaaSecurity()} />

        {/* Error State */}
        {error && (
          <Card
            className="border-red-200 bg-red-50"
            style={{
              backgroundColor: '#FEF2F2',
              borderColor: '#FECACA',
              color: '#333333'
            }}
          >
            <CardContent className="p-6">
              <div className="flex items-center gap-3" style={{ color: '#EF4444' }}>
                <AlertTriangle className="h-6 w-6" />
                <div>
                  <h3 className="font-semibold" style={{ color: '#333333' }}>Error Loading Scans</h3>
                  <p className="text-sm" style={{ color: '#666666' }}>{error}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Scans List */}
        {scans.length === 0 ? (
          <Card style={{ backgroundColor: 'white', borderColor: '#E5E5E5' }}>
            <CardContent className="p-12">
              <div className="text-center">
                <Lock className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2" style={{ color: '#333333' }}>No Security Scans Yet</h3>
                <p className="mb-6 max-w-md mx-auto" style={{ color: '#666666' }}>
                  Start your first HIPAA security compliance scan to see results here.
                </p>
                <Button
                  asChild
                  style={{
                    backgroundColor: '#0055A4',
                    color: 'white',
                    border: 'none',
                  }}
                  className="hover:bg-blue-700 transition-colors shadow-md"
                >
                  <Link href="/dashboard/hipaa/security-scan">
                    <Plus className="h-4 w-4 mr-2" />
                    Start Your First Security Scan
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {scans.map((scan) => (
              <Card
                key={scan.id}
                className="hover:shadow-md transition-shadow"
                style={{
                  backgroundColor: 'white',
                  borderColor: '#E5E5E5',
                  color: '#333333'
                }}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold truncate" style={{ color: '#333333' }}>
                          {scan.targetUrl}
                        </h3>
                        <Badge variant={getStatusColor(scan.status) as any}>
                          {scan.status.toUpperCase()}
                        </Badge>
                        <Badge variant={getRiskLevelColor(scan.riskLevel) as any}>
                          {scan.riskLevel.toUpperCase()} RISK
                        </Badge>
                      </div>

                      <div className="flex items-center gap-6 text-sm" style={{ color: '#666666' }}>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {formatDate(scan.timestamp)}
                        </div>
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-4 w-4" />
                          Score: {scan.overallScore}%
                        </div>
                        <div className="flex items-center gap-1">
                          <CheckCircle className="h-4 w-4" style={{ color: '#22C55E' }} />
                          {scan.passedTests} passed
                        </div>
                        <div className="flex items-center gap-1">
                          <AlertTriangle className="h-4 w-4" style={{ color: '#EF4444' }} />
                          {scan.failedTests} failed
                        </div>
                        {scan.vulnerabilities > 0 && (
                          <div className="flex items-center gap-1">
                            <Shield className="h-4 w-4" style={{ color: '#F59E0B' }} />
                            {scan.vulnerabilities} vulnerabilities
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        asChild
                        style={{
                          borderColor: '#0055A4',
                          color: '#0055A4',
                          backgroundColor: 'white',
                        }}
                        className="hover:bg-blue-50 transition-colors"
                      >
                        <Link href={scan.targetUrl} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button
                        size="sm"
                        asChild
                        style={{
                          backgroundColor: '#0055A4',
                          color: 'white',
                          border: 'none',
                        }}
                        className="hover:bg-blue-700 transition-colors shadow-md"
                      >
                        <Link href={`/dashboard/hipaa/security/${scan.id}`}>View Results</Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button asChild className="h-auto p-4 flex-col gap-2">
                <Link href="/dashboard/hipaa/security-scan">
                  <Lock className="h-6 w-6" />
                  <span>Start New Security Scan</span>
                </Link>
              </Button>
              <Button asChild className="h-auto p-4 flex-col gap-2" variant="outline">
                <Link href="/dashboard/hipaa/privacy">
                  <FileText className="h-6 w-6" />
                  <span>View Privacy Scans</span>
                </Link>
              </Button>
              <Button asChild className="h-auto p-4 flex-col gap-2" variant="outline">
                <Link href="/guidance">
                  <CheckCircle className="h-6 w-6" />
                  <span>Compliance Guidance</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
